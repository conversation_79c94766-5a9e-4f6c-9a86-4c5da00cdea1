import { EventEmitter } from 'events';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { QueuedMessage } from '../models/types';
import { MessageQueueService } from '../core/MessageQueueService';
import { MessageValidationService } from '../core/MessageValidationService';
import { ConversationService } from '../core/ConversationService';
import { NotificationDispatcher, NotificationMessage } from './NotificationDispatcher';
import { logger } from '../utils/Logger';

// ===== EVENT-DRIVEN MESSAGE PROCESSOR =====

export interface MessageEvent {
  type: 'queued' | 'validated' | 'ready' | 'sent' | 'failed' | 'withdrawn';
  messageId: number;
  conversationId: number;
  character: string;
  timestamp: Date;
  data?: any;
}

export class EventDrivenMessageProcessor extends EventEmitter {
  private static instance: EventDrivenMessageProcessor;
  private notificationDispatcher: NotificationDispatcher;
  private processingMessages: Set<number> = new Set();
  
  // Timers for delayed message processing
  private messageTimers: Map<number, NodeJS.Timeout> = new Map();
  
  // Conversation processing state
  private conversationProcessors: Map<number, boolean> = new Map();

  private constructor() {
    super();
    this.notificationDispatcher = NotificationDispatcher.getInstance();
    this.setupEventHandlers();
  }

  static getInstance(): EventDrivenMessageProcessor {
    if (!EventDrivenMessageProcessor.instance) {
      EventDrivenMessageProcessor.instance = new EventDrivenMessageProcessor();
    }
    return EventDrivenMessageProcessor.instance;
  }

  // ===== EVENT HANDLING =====

  private setupEventHandlers(): void {
    // Handle message queued events
    this.on('message_queued', this.handleMessageQueued.bind(this));

    // Handle validation completed events (fix: use 'message_validated' to match emitted events)
    this.on('message_validated', this.handleValidationCompleted.bind(this));

    // Handle message ready events
    this.on('message_ready', this.handleMessageReady.bind(this));

    // Handle message sent events
    this.on('message_sent', this.handleMessageSent.bind(this));
  }

  // ===== PUBLIC API =====

  /**
   * Start processing messages for a conversation
   */
  startConversationProcessing(conversationId: number): void {
    if (this.conversationProcessors.get(conversationId)) {
      logger.debug(`Message processing already active for conversation ${conversationId}`);
      return;
    }

    this.conversationProcessors.set(conversationId, true);
    logger.info(`🚀 Started event-driven message processing for conversation ${conversationId}`);

    // Process any existing messages that need attention
    this.processExistingMessages(conversationId);
  }

  /**
   * Stop processing messages for a conversation
   */
  stopConversationProcessing(conversationId: number): void {
    this.conversationProcessors.delete(conversationId);
    
    // Clear any pending timers for this conversation
    for (const [messageId, timer] of this.messageTimers.entries()) {
      // We'd need to track conversation ID per timer, for now clear all
      // This is a simplification - in production we'd want better tracking
      clearTimeout(timer);
      this.messageTimers.delete(messageId);
    }
    
    logger.info(`🛑 Stopped event-driven message processing for conversation ${conversationId}`);
  }

  /**
   * Emit a message event to trigger processing
   */
  emitMessageEvent(event: MessageEvent): void {
    logger.debug(`📨 Message event: ${event.type} for message ${event.messageId} (${event.character})`);
    this.emit(`message_${event.type}`, event);
  }

  // ===== EVENT HANDLERS =====

  private async handleMessageQueued(event: MessageEvent): Promise<void> {
    try {
      logger.info(`📥 Processing queued message ${event.messageId} from ${event.character}`);
      
      // Check if message needs validation
      const message = await MessageQueueService.getMessageById(event.messageId);
      if (!message) {
        logger.error(`Message ${event.messageId} not found for validation`);
        return;
      }

      // Start validation process
      this.startMessageValidation(message);
      
    } catch (error) {
      logger.error(`Error handling queued message ${event.messageId}`, error);
    }
  }

  private async handleValidationCompleted(event: MessageEvent): Promise<void> {
    try {
      logger.info(`✅ Validation completed for message ${event.messageId} from ${event.character}`);

      const message = await MessageQueueService.getMessageById(event.messageId);
      if (!message) {
        logger.error(`Message ${event.messageId} not found after validation`);
        return;
      }

      logger.info(`📋 Message ${event.messageId} validation decision: ${message.validation_decision}, status: ${message.status}, validation_status: ${message.validation_status}`);

      // Check if message is ready to be sent
      if (message.validation_decision === 'SEND_AS_IS' || message.validation_decision === 'REVISE') {
        logger.info(`🚀 Scheduling delivery for message ${event.messageId} (decision: ${message.validation_decision})`);
        this.scheduleMessageDelivery(message);
      } else if (message.validation_decision === 'WITHDRAW') {
        logger.info(`❌ Message ${event.messageId} was withdrawn by ${event.character}`);
        this.emitMessageEvent({
          type: 'withdrawn',
          messageId: event.messageId,
          conversationId: event.conversationId,
          character: event.character,
          timestamp: new Date()
        });
      } else {
        logger.warn(`⚠️ Message ${event.messageId} has unexpected validation decision: ${message.validation_decision}`);
      }

    } catch (error) {
      logger.error(`Error handling validation completion for message ${event.messageId}`, error);
    }
  }

  private async handleMessageReady(event: MessageEvent): Promise<void> {
    try {
      logger.info(`🚀 Message ${event.messageId} from ${event.character} is ready for delivery`);

      const message = await MessageQueueService.getMessageById(event.messageId);
      if (!message) {
        logger.error(`❌ Message ${event.messageId} not found for delivery`);
        return;
      }

      logger.info(`📦 Delivering message ${event.messageId}: "${message.text.substring(0, 50)}..." (status: ${message.status}, validation: ${message.validation_decision})`);

      // Deliver the message
      await this.deliverMessage(message);

    } catch (error) {
      logger.error(`❌ Error handling ready message ${event.messageId}`, error);
    }
  }

  private async handleMessageSent(event: MessageEvent): Promise<void> {
    try {
      logger.info(`📤 Message ${event.messageId} has been sent`);
      
      // Clean up processing state
      this.processingMessages.delete(event.messageId);
      
      // Clear any timers
      const timer = this.messageTimers.get(event.messageId);
      if (timer) {
        clearTimeout(timer);
        this.messageTimers.delete(event.messageId);
      }
      
    } catch (error) {
      logger.error(`Error handling sent message ${event.messageId}`, error);
    }
  }

  // ===== PROCESSING METHODS =====

  private async processExistingMessages(conversationId: number): Promise<void> {
    try {
      // Check for messages needing validation
      const messagesNeedingValidation = await MessageQueueService.getMessagesNeedingValidation(conversationId);
      
      for (const message of messagesNeedingValidation) {
        this.startMessageValidation(message);
      }

      // Check for validated messages ready to be sent
      const readyMessages = await MessageQueueService.getValidatedReadyMessages(conversationId);
      
      for (const message of readyMessages) {
        this.scheduleMessageDelivery(message);
      }
      
      logger.info(`🔄 Processed ${messagesNeedingValidation.length} validation-pending and ${readyMessages.length} ready messages for conversation ${conversationId}`);
      
    } catch (error) {
      logger.error(`Error processing existing messages for conversation ${conversationId}`, error);
    }
  }

  private async startMessageValidation(message: QueuedMessage): Promise<void> {
    if (this.processingMessages.has(message.id)) {
      logger.debug(`Message ${message.id} is already being processed`);
      return;
    }

    this.processingMessages.add(message.id);
    
    try {
      // Start validation (don't await - let it run in background)
      MessageValidationService.validateQueuedMessage(message.id)
        .then(() => {
          // Clean up processing state after successful validation
          this.processingMessages.delete(message.id);

          this.emitMessageEvent({
            type: 'validated',
            messageId: message.id,
            conversationId: message.conversation_id,
            character: message.character,
            timestamp: new Date()
          });
        })
        .catch(error => {
          logger.error(`Validation failed for message ${message.id}`, error);
          this.processingMessages.delete(message.id);
        });
        
    } catch (error) {
      logger.error(`Error starting validation for message ${message.id}`, error);
      this.processingMessages.delete(message.id);
    }
  }

  private scheduleMessageDelivery(message: QueuedMessage): void {
    if (this.messageTimers.has(message.id)) {
      logger.debug(`Message ${message.id} delivery already scheduled`);
      return;
    }

    const now = Date.now();
    const scheduledTime = new Date(message.scheduled_at!).getTime();
    const delay = Math.max(0, scheduledTime - now);

    logger.info(`⏰ Scheduling message ${message.id} from ${message.character} for delivery in ${delay}ms (scheduled: ${message.scheduled_at})`);

    const timer = setTimeout(() => {
      logger.info(`🔔 Message ${message.id} delivery timer triggered, emitting ready event`);
      this.emitMessageEvent({
        type: 'ready',
        messageId: message.id,
        conversationId: message.conversation_id,
        character: message.character,
        timestamp: new Date()
      });
    }, delay);

    this.messageTimers.set(message.id, timer);
    logger.info(`✅ Message ${message.id} delivery timer set (${this.messageTimers.size} total timers active)`);
  }

  private async deliverMessage(message: QueuedMessage): Promise<void> {
    try {
      logger.info(`🔄 Starting delivery for message ${message.id} from ${message.character}`);

      // Mark as processing
      await MessageQueueService.updateMessageStatus(message.id, 'PROCESSING');
      logger.info(`📝 Updated message ${message.id} status to PROCESSING`);

      // Send notification to all clients in the conversation
      const notification: NotificationMessage = {
        id: this.notificationDispatcher.generateMessageId(),
        type: 'message',
        data: {
          character: message.character,
          text: message.text,
          messageId: message.id,
          timestamp: new Date()
        },
        timestamp: new Date(),
        priority: 2,
        conversationId: message.conversation_id
      };

      logger.info(`📡 Sending notification for message ${message.id} to conversation ${message.conversation_id}`);
      const deliveryResults = await this.notificationDispatcher.notifyConversation(message.conversation_id, notification);
      logger.info(`📡 Notification sent to ${deliveryResults.length} clients for message ${message.id}`);

      // Add to conversation history
      const messageExists = await ConversationService.checkMessageExists(
        message.character,
        message.text,
        message.conversation_id
      );

      if (!messageExists) {
        await ConversationService.addMessage(message.character, message.text, message.conversation_id);
        logger.info(`💾 Added message ${message.id} to conversation history`);
      } else {
        logger.info(`💾 Message ${message.id} already exists in conversation history`);
      }

      // Mark as sent
      await MessageQueueService.updateMessageStatus(message.id, 'SENT');
      logger.info(`✅ Updated message ${message.id} status to SENT`);

      // Emit sent event
      this.emitMessageEvent({
        type: 'sent',
        messageId: message.id,
        conversationId: message.conversation_id,
        character: message.character,
        timestamp: new Date()
      });

      logger.info(`📤 Successfully delivered message ${message.id} from ${message.character} to conversation ${message.conversation_id}`);

    } catch (error) {
      logger.error(`❌ Error delivering message ${message.id}`, error);

      // Mark as failed
      await MessageQueueService.updateMessageStatus(message.id, 'FAILED');

      this.emitMessageEvent({
        type: 'failed',
        messageId: message.id,
        conversationId: message.conversation_id,
        character: message.character,
        timestamp: new Date(),
        data: { error: (error as Error).message }
      });
    }
  }

  // ===== DEBUG UTILITIES =====

  /**
   * Get debug information about message processing state
   */
  async getDebugInfo(conversationId: number): Promise<{
    processingMessages: number[];
    activeTimers: number[];
    conversationActive: boolean;
    queueInfo: any;
  }> {
    const queueInfo = await MessageQueueService.getQueueDebugInfo(conversationId);

    return {
      processingMessages: Array.from(this.processingMessages),
      activeTimers: Array.from(this.messageTimers.keys()),
      conversationActive: this.conversationProcessors.has(conversationId),
      queueInfo
    };
  }

  /**
   * Force process any stuck messages for debugging
   */
  async forceProcessStuckMessages(conversationId: number): Promise<void> {
    logger.info(`🔧 Force processing stuck messages for conversation ${conversationId}`);

    // Check for validated messages that should be ready
    const readyMessages = await MessageQueueService.getValidatedReadyMessages(conversationId);
    logger.info(`🔍 Found ${readyMessages.length} validated ready messages`);

    for (const message of readyMessages) {
      if (!this.messageTimers.has(message.id)) {
        logger.info(`🚀 Force scheduling delivery for message ${message.id}`);
        this.scheduleMessageDelivery(message);
      }
    }
  }

  // ===== UTILITY METHODS =====

  getProcessingStats(): {
    activeConversations: number;
    processingMessages: number;
    scheduledMessages: number;
  } {
    return {
      activeConversations: this.conversationProcessors.size,
      processingMessages: this.processingMessages.size,
      scheduledMessages: this.messageTimers.size
    };
  }
}
